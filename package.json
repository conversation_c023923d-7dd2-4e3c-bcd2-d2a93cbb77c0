{"name": "lora-x402", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.80.7", "@wagmi/connectors": "^5.8.5", "@wagmi/core": "^2.17.3", "lucide-react": "^0.517.0", "next": "15.3.3", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "replicate": "^1.0.1", "viem": "^2.31.3", "wagmi": "^2.15.6", "x402-fetch": "^0.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}
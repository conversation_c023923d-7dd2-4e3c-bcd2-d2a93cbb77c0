'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { getModelById } from '@/data/models';
import PlaygroundInput from '@/components/PlaygroundInput';
import PlaygroundOutput from '@/components/PlaygroundOutput';
import PaymentStatus from '@/components/PaymentStatus';
import { GenerationResult } from '@/types';
import { generateImage } from '@/services/api';
import { useAccount } from 'wagmi';
import { processRealX402Payment, mockX402Payment } from '@/services/x402';

export default function PlaygroundPage() {
  const params = useParams();
  const modelId = params.model as string;
  const model = getModelById(modelId);
  const { address, isConnected } = useAccount();

  const [isGenerating, setIsGenerating] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [paymentError, setPaymentError] = useState<string | undefined>();
  const [result, setResult] = useState<GenerationResult | null>(null);

  if (!model) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <h1 className="text-2xl font-bold mb-4">Model Not Found</h1>
          <p className="text-gray-400">The requested model could not be found.</p>
        </div>
      </div>
    );
  }

  const handleGenerate = async (prompt: string, customParams?: any) => {
    // Check wallet connection
    if (!isConnected || !address) {
      setPaymentStatus('error');
      setPaymentError('Please connect your wallet first to generate images.');
      return;
    }

    // Reset previous states
    setPaymentStatus('processing');
    setPaymentError(undefined);
    setIsProcessingPayment(true);

    try {
      // Process x402 payment first
      console.log('Processing x402 payment...');
      const paymentResult = await processRealX402Payment('$1', 'LoRA image generation');

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment failed');
      }

      console.log('Payment successful, generating image...');
      setPaymentStatus('success');
      setIsProcessingPayment(false);
      setIsGenerating(true);

      // Small delay to show success status
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Proceed with image generation after successful payment
      const generationResult = await generateImage({
        prompt,
        modelId,
        ...customParams,
      });
      setResult(generationResult);

      // Reset payment status after successful generation
      setTimeout(() => setPaymentStatus('idle'), 3000);
    } catch (error) {
      console.error('Generation failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setPaymentStatus('error');
      setPaymentError(errorMessage);
    } finally {
      setIsGenerating(false);
      setIsProcessingPayment(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Navigation */}
      <nav className="border-b border-gray-800 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <h1 className="text-white font-semibold">{model.provider}/{model.name}</h1>
              <div className="flex space-x-6">
                <button className="text-white border-b-2 border-blue-500 pb-4 px-1">
                  Playground
                </button>
                <button className="text-gray-400 hover:text-white pb-4 px-1">
                  API
                </button>
                <button className="text-gray-400 hover:text-white pb-4 px-1">
                  Examples
                </button>
                <button className="text-gray-400 hover:text-white pb-4 px-1">
                  README
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-4rem)]">
        {/* Input Panel */}
        <div className="w-1/2 border-r border-gray-800">
          <div className="h-full flex flex-col">
            <div className="flex-1">
              <PlaygroundInput
                onGenerate={handleGenerate}
                isGenerating={isGenerating}
                isProcessingPayment={isProcessingPayment}
                model={model}
              />
            </div>

            {/* Payment Status */}
            <div className="p-4 border-t border-gray-800">
              <PaymentStatus
                status={paymentStatus}
                error={paymentError}
                amount="$1"
              />
            </div>
          </div>
        </div>

        {/* Output Panel */}
        <div className="w-1/2">
          <PlaygroundOutput 
            result={result} 
            isGenerating={isGenerating}
          />
        </div>
      </div>
    </div>
  );
}

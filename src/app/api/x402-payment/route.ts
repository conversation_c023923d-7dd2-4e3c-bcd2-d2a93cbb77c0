import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { amount, description } = body;

    // Check if this is a payment request (no x-payment header)
    const paymentHeader = request.headers.get('x-payment');
    
    if (!paymentHeader) {
      // Return 402 Payment Required with payment instructions
      return NextResponse.json(
        {
          error: 'Payment Required',
          message: 'This endpoint requires payment to access',
          payment: {
            amount: amount || '$1',
            description: description || 'LoRA image generation payment',
            facilitator: 'https://x402.org/facilitator',
            network: 'base-sepolia',
          },
        },
        { 
          status: 402,
          headers: {
            'Content-Type': 'application/json',
            'X-Payment-Required': 'true',
            'X-Payment-Amount': amount || '$1',
            'X-Payment-Description': description || 'LoRA image generation payment',
          }
        }
      );
    }

    // If payment header is present, verify payment (mock verification for now)
    // In a real implementation, you would verify the payment with the facilitator
    
    // Mock payment verification
    const isPaymentValid = true; // In real implementation, verify with facilitator
    
    if (!isPaymentValid) {
      return NextResponse.json(
        { error: 'Invalid payment' },
        { status: 402 }
      );
    }

    // Payment successful, return success response
    return NextResponse.json(
      {
        success: true,
        message: 'Payment verified successfully',
        paymentDetails: {
          amount: amount || '$1',
          description: description || 'LoRA image generation payment',
          timestamp: new Date().toISOString(),
        },
      },
      {
        headers: {
          'X-Payment-Response': JSON.stringify({
            verified: true,
            amount: amount || '$1',
            timestamp: new Date().toISOString(),
          }),
        },
      }
    );
  } catch (error) {
    console.error('x402 payment API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Payment processing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Payment',
    },
  });
}

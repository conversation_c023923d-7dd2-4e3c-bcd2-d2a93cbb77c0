import { NextRequest, NextResponse } from 'next/server';
import { generateWithReplicate, validateReplicateToken } from '@/services/replicate';
import { getModelById } from '@/data/models';

export async function POST(request: NextRequest) {
  try {
    // Validate API token
    if (!validateReplicateToken()) {
      return NextResponse.json(
        { error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { prompt, modelId, ...customParams } = body;

    // Validate required fields
    if (!prompt || !modelId) {
      return NextResponse.json(
        { error: 'Missing required fields: prompt and modelId' },
        { status: 400 }
      );
    }

    // Get model configuration
    const model = getModelById(modelId);
    if (!model) {
      return NextResponse.json(
        { error: `Model not found: ${modelId}` },
        { status: 404 }
      );
    }

    if (!model.replicateModel) {
      return NextResponse.json(
        { error: `Model ${modelId} is not configured for Replicate` },
        { status: 400 }
      );
    }

    // Prepare the final prompt with base prompt if available
    const finalPrompt = model.basePrompt
      ? `${model.basePrompt}, ${prompt}`
      : prompt;

    console.log(`Original prompt: "${prompt}"`);
    console.log(`Final prompt with base: "${finalPrompt}"`);

    // Generate image
    const result = await generateWithReplicate({
      prompt: finalPrompt,
      model,
      ...customParams,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Generation API error:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Generation failed',
        details: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

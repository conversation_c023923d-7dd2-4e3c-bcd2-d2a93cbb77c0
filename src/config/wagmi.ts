import { http, createConfig } from 'wagmi'
import { baseSepolia } from 'wagmi/chains'
import { metaMask, walletConnect } from 'wagmi/connectors'

// WalletConnect project ID (you can get this from https://cloud.walletconnect.com)
const projectId = 'your-project-id' // Replace with actual project ID

export const config = createConfig({
  chains: [baseSepolia],
  connectors: [
    metaMask(),
    walletConnect({ 
      projectId,
      metadata: {
        name: 'LoRAs x402',
        description: 'Generate images with LoRA models using crypto payments',
        url: 'https://localhost:3000',
        icons: ['https://localhost:3000/favicon.ico']
      }
    }),
  ],
  transports: {
    [baseSepolia.id]: http(),
  },
})

declare module 'wagmi' {
  interface Register {
    config: typeof config
  }
}

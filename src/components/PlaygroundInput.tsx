'use client';

import { useState } from 'react';
import { Model } from '@/types';
import { Loader2, Wallet, DollarSign } from 'lucide-react';
import { useAccount } from 'wagmi';
import PaymentSplit from './PaymentSplit';

interface PlaygroundInputProps {
  onGenerate: (prompt: string, params?: any) => void;
  isGenerating: boolean;
  isProcessingPayment?: boolean;
  model: Model;
}

export default function PlaygroundInput({ onGenerate, isGenerating, isProcessingPayment = false, model }: PlaygroundInputProps) {
  const [prompt, setPrompt] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [loraScale, setLoraScale] = useState(1);
  const [guidanceScale, setGuidanceScale] = useState(3);
  const [numSteps, setNumSteps] = useState(28);
  const { isConnected, address } = useAccount();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim() && !isGenerating && !isProcessingPayment) {
      const params = {
        lora_scale: loraScale,
        guidance_scale: guidanceScale,
        num_inference_steps: numSteps,
      };
      onGenerate(prompt.trim(), params);
    }
  };

  const isDisabled = !prompt.trim() || isGenerating || isProcessingPayment || !isConnected;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex space-x-4">
          <button className="bg-gray-700 text-white px-3 py-1 rounded text-sm">
            Form
          </button>
          <button className="text-gray-400 hover:text-white px-3 py-1 rounded text-sm">
            JSON
          </button>
        </div>
      </div>

      {/* Form */}
      <div className="flex-1 p-4">
        <form onSubmit={handleSubmit} className="h-full flex flex-col">
          {/* Payment Section */}
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h3 className="text-white font-medium mb-3">Payment Required</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Cost per generation:</span>
                <span className="text-green-400 font-medium">$1 USDC</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Network:</span>
                <span className="text-white">Base Sepolia</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Wallet:</span>
                <div className="flex items-center gap-2">
                  {isConnected ? (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-green-400">Connected</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-red-400">Not Connected</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Payment Split Visualization */}
            <PaymentSplit />
          </div>

          <div className="flex-1">
            <label htmlFor="prompt" className="block text-white text-sm font-medium mb-2">
              prompt*
            </label>

            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your prompt here"
              className="w-full h-32 bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              disabled={isGenerating}
            />
            <p className="text-gray-400 text-xs mt-2">
              Text description of what you want to generate. Be as descriptive as possible for best results.
            </p>
          </div>

          {/* Advanced Parameters */}
          <div className="mt-6">
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-blue-400 hover:text-blue-300 text-sm font-medium mb-3"
            >
              {showAdvanced ? '▼' : '▶'} Advanced Parameters
            </button>

            {showAdvanced && (
              <div className="space-y-4 p-4 bg-gray-800 rounded-lg">
                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    LoRA Scale: {loraScale}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={loraScale}
                    onChange={(e) => setLoraScale(parseFloat(e.target.value))}
                    className="w-full"
                    disabled={isGenerating}
                  />
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    Guidance Scale: {guidanceScale}
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    step="0.5"
                    value={guidanceScale}
                    onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                    className="w-full"
                    disabled={isGenerating}
                  />
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    Inference Steps: {numSteps}
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="50"
                    step="1"
                    value={numSteps}
                    onChange={(e) => setNumSteps(parseInt(e.target.value))}
                    className="w-full"
                    disabled={isGenerating}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Model Info */}
          <div className="mt-6 p-4 bg-gray-800 rounded-lg">
            <h3 className="text-white font-medium mb-2">Model Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Provider:</span>
                <span className="text-white">{model.provider}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Model:</span>
                <span className="text-white">{model.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Runs:</span>
                <span className="text-white">{model.runs}</span>
              </div>
              {model.replicateModel && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Replicate:</span>
                  <span className="text-white text-xs">{model.replicateModel}</span>
                </div>
              )}
            </div>
          </div>



          {/* Generate Button */}
          <div className="mt-6">
            <button
              type="submit"
              disabled={isDisabled}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center"
            >
              {isProcessingPayment ? (
                <>
                  <DollarSign className="w-4 h-4 mr-2 animate-pulse" />
                  Processing Payment...
                </>
              ) : isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : !isConnected ? (
                <>
                  <Wallet className="w-4 h-4 mr-2" />
                  Connect Wallet to Generate
                </>
              ) : (
                <>
                  <DollarSign className="w-4 h-4 mr-2" />
                  Pay $1 & Generate
                </>
              )}
            </button>

            {!isConnected && (
              <p className="text-gray-400 text-xs mt-2 text-center">
                Connect your wallet in the top navigation to start generating images
              </p>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

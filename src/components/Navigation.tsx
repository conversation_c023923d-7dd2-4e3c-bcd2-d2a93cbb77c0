'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Sparkles } from 'lucide-react';
import WalletConnect from './WalletConnect';

export default function Navigation() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    if (path === '/' && pathname === '/') return true;
    if (path !== '/' && pathname.startsWith(path)) return true;
    return false;
  };

  return (
    <nav className="bg-gray-900 border-b border-gray-800">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Sparkles className="w-6 h-6 text-blue-500" />
            <span className="text-white font-bold text-lg">LoRAs x402</span>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center space-x-6">
            <div className="flex space-x-6">
              <Link
                href="/"
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive('/') && pathname === '/'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-700'
                }`}
              >
                LoRAs
              </Link>
            </div>

            {/* Wallet Connection */}
            <WalletConnect />
          </div>
        </div>
      </div>
    </nav>
  );
}

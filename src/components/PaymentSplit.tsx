'use client'

import { Brain, User, Palette } from 'lucide-react'

export default function PaymentSplit() {
  const splits = [
    {
      icon: <Brain className="w-4 h-4" />,
      label: 'AI Model',
      percentage: 33,
      color: 'bg-blue-500',
      lightColor: 'bg-blue-100',
      textColor: 'text-blue-600'
    },
    {
      icon: <User className="w-4 h-4" />,
      label: 'LoRA Creator',
      percentage: 33,
      color: 'bg-purple-500',
      lightColor: 'bg-purple-100',
      textColor: 'text-purple-600'
    },
    {
      icon: <Palette className="w-4 h-4" />,
      label: 'Artist',
      percentage: 34, // 34% to make it add up to 100%
      color: 'bg-green-500',
      lightColor: 'bg-green-100',
      textColor: 'text-green-600'
    }
  ]

  return (
    <div className="mt-3 p-3 bg-gray-700 rounded-md">
      <div className="flex items-center gap-2 mb-3">
        <span className="text-gray-300 text-xs font-medium">PAYMENT SPLIT:</span>
      </div>
      
      {/* Visual Bar */}
      <div className="flex rounded-full overflow-hidden h-2 mb-3">
        {splits.map((split, index) => (
          <div
            key={index}
            className={split.color}
            style={{ width: `${split.percentage}%` }}
          />
        ))}
      </div>
      
      {/* Split Details */}
      <div className="grid grid-cols-3 gap-2 text-xs">
        {splits.map((split, index) => (
          <div key={index} className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${split.color}`} />
            <div className="flex items-center gap-1 text-gray-300">
              {split.icon}
              <span>{split.label}</span>
            </div>
            <span className="text-gray-400 ml-auto">{split.percentage}%</span>
          </div>
        ))}
      </div>
      
      {/* Amount Breakdown */}
      <div className="mt-3 pt-2 border-t border-gray-600">
        <div className="grid grid-cols-3 gap-2 text-xs">
          {splits.map((split, index) => (
            <div key={index} className="text-center">
              <div className="text-gray-400">~$0.{split.percentage}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

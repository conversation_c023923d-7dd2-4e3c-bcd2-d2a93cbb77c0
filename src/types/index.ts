export interface Model {
  id: string;
  provider: string;
  name: string;
  title: string;
  description?: string;
  previewImage: string;
  updated: string;
  runs: string;
  replicateModel?: string;
  replicateVersion?: string;
  basePrompt?: string;
}

export interface GeneratedImage {
  id: string;
  imageUrl: string;
  prompt: string;
  generationTime: string;
  modelId: string;
  createdAt: string;
}

export interface GenerationResult {
  imageUrl: string;
  generationTime: string;
  prompt: string;
}

import { wrapFetchWithPayment, decodeXPaymentResponse } from 'x402-fetch';
import { Account, createWalletClient, custom } from 'viem';
import { baseSepolia } from 'viem/chains';

// Extend window type for MetaMask
declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      isMetaMask?: boolean;
    };
  }
}

export interface X402PaymentResult {
  success: boolean;
  paymentResponse?: any;
  error?: string;
}

export interface X402PaymentRequest {
  url: string;
  amount: string; // e.g., "$1"
  description?: string;
}

// Create a mock x402 payment endpoint for testing
export const createX402PaymentEndpoint = (baseUrl: string) => {
  return `${baseUrl}/api/x402-payment`;
};

// Process x402 payment using the account
export async function processX402Payment(
  account: Account,
  paymentRequest: X402PaymentRequest
): Promise<X402PaymentResult> {
  try {
    // Wrap fetch with x402 payment capability
    const fetchWithPayment = wrapFetchWithPayment(fetch, account);
    
    // Make the payment request
    const response = await fetchWithPayment(paymentRequest.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: paymentRequest.amount,
        description: paymentRequest.description || 'LoRA image generation payment',
      }),
    });

    if (!response.ok) {
      throw new Error(`Payment failed: ${response.status} ${response.statusText}`);
    }

    const body = await response.json();
    
    // Decode payment response from headers
    const paymentResponseHeader = response.headers.get('x-payment-response');
    const paymentResponse = paymentResponseHeader 
      ? decodeXPaymentResponse(paymentResponseHeader)
      : null;

    return {
      success: true,
      paymentResponse,
    };
  } catch (error) {
    console.error('x402 payment error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment failed',
    };
  }
}

// Get wallet client from browser
export function getWalletClient(): Account | null {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null;
  }

  try {
    const walletClient = createWalletClient({
      chain: baseSepolia,
      transport: custom(window.ethereum)
    });

    // For x402, we need to create an account-like object
    // This is a simplified approach - in production you'd want proper account handling
    return walletClient as any;
  } catch (error) {
    console.error('Failed to create wallet client:', error);
    return null;
  }
}

// Simplified payment verification that triggers Metamask
export async function processRealX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  try {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('MetaMask not available');
    }

    // Request account access if needed
    const accounts = await window.ethereum.request({
      method: 'eth_requestAccounts'
    });

    if (!accounts || accounts.length === 0) {
      throw new Error('No accounts available');
    }

    // For now, we'll simulate a payment by showing the user's address
    // In a real implementation, you would:
    // 1. Create a payment transaction
    // 2. Send it to the user for signing
    // 3. Verify the transaction on-chain

    console.log('Connected account:', accounts[0]);
    console.log('Simulating payment for:', amount);

    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // For development, return success after showing Metamask connection
    return {
      success: true,
      paymentResponse: {
        amount,
        description: description || 'LoRA image generation payment',
        timestamp: new Date().toISOString(),
        transactionId: `sim_tx_${Date.now()}`,
        account: accounts[0],
      },
    };
  } catch (error) {
    console.error('Real x402 payment error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment failed',
    };
  }
}

// Mock x402 payment verification for development
export async function mockX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  // Simulate payment processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // For development, always return success
  return {
    success: true,
    paymentResponse: {
      amount,
      description,
      timestamp: new Date().toISOString(),
      transactionId: `mock_tx_${Date.now()}`,
    },
  };
}

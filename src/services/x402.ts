import { createWalletClient, custom, parseUnits, formatUnits } from 'viem';
import { baseSepolia } from 'viem/chains';
import { wrapFetchWithPayment, decodeXPaymentResponse } from 'x402-fetch';
import type { Account } from 'viem';

// USDC contract address on Base Sepolia
const USDC_CONTRACT_ADDRESS = '******************************************';

// Payment recipient address
const PAYMENT_RECIPIENT = '******************************************';

// USDC contract ABI (minimal for transfer)
const USDC_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "to", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "transfer",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
] as const;

// Extend window type for MetaMask
declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      isMetaMask?: boolean;
    };
  }
}

export interface X402PaymentResult {
  success: boolean;
  paymentResponse?: any;
  error?: string;
}

export interface X402PaymentRequest {
  url: string;
  amount: string; // e.g., "$1"
  description?: string;
}

// Create a mock x402 payment endpoint for testing
export const createX402PaymentEndpoint = (baseUrl: string) => {
  return `${baseUrl}/api/x402-payment`;
};

// Process x402 payment using the account
export async function processX402Payment(
  account: Account,
  paymentRequest: X402PaymentRequest
): Promise<X402PaymentResult> {
  try {
    // Wrap fetch with x402 payment capability
    const fetchWithPayment = wrapFetchWithPayment(fetch, account);
    
    // Make the payment request
    const response = await fetchWithPayment(paymentRequest.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: paymentRequest.amount,
        description: paymentRequest.description || 'LoRA image generation payment',
      }),
    });

    if (!response.ok) {
      throw new Error(`Payment failed: ${response.status} ${response.statusText}`);
    }

    const body = await response.json();
    
    // Decode payment response from headers
    const paymentResponseHeader = response.headers.get('x-payment-response');
    const paymentResponse = paymentResponseHeader 
      ? decodeXPaymentResponse(paymentResponseHeader)
      : null;

    return {
      success: true,
      paymentResponse,
    };
  } catch (error) {
    console.error('x402 payment error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment failed',
    };
  }
}

// Get wallet client from browser
export function getWalletClient(): Account | null {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null;
  }

  try {
    const walletClient = createWalletClient({
      chain: baseSepolia,
      transport: custom(window.ethereum)
    });

    // For x402, we need to create an account-like object
    // This is a simplified approach - in production you'd want proper account handling
    return walletClient as any;
  } catch (error) {
    console.error('Failed to create wallet client:', error);
    return null;
  }
}

// Real USDC payment to specified wallet
export async function processRealX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  try {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('MetaMask not available');
    }

    console.log('Starting payment process...');

    // Request account access if needed
    const accounts = await window.ethereum.request({
      method: 'eth_requestAccounts'
    });

    if (!accounts || accounts.length === 0) {
      throw new Error('No accounts available');
    }

    const userAccount = accounts[0];
    console.log('Connected account:', userAccount);
    console.log('Sending payment to:', PAYMENT_RECIPIENT);

    // Check if we're on the correct network
    const chainId = await window.ethereum.request({ method: 'eth_chainId' });
    console.log('Current chain ID:', chainId);

    // Base Sepolia chain ID is 0x14a34 (84532 in decimal)
    const baseSepoliaChainId = '0x14a34';

    if (chainId !== baseSepoliaChainId) {
      // Request network switch
      try {
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: baseSepoliaChainId }],
        });
      } catch (switchError: any) {
        // If the network doesn't exist, add it
        if (switchError.code === 4902) {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [{
              chainId: baseSepoliaChainId,
              chainName: 'Base Sepolia',
              nativeCurrency: {
                name: 'ETH',
                symbol: 'ETH',
                decimals: 18,
              },
              rpcUrls: ['https://sepolia.base.org'],
              blockExplorerUrls: ['https://sepolia-explorer.base.org'],
            }],
          });
        } else {
          throw switchError;
        }
      }
    }

    // Create wallet client without account (let it handle account internally)
    const walletClient = createWalletClient({
      chain: baseSepolia,
      transport: custom(window.ethereum)
    });

    // Parse amount (assuming $1 = 1 USDC with 6 decimals)
    const usdcAmount = parseUnits('1', 6); // 1 USDC = 1,000,000 units (6 decimals)

    console.log('Sending USDC amount:', formatUnits(usdcAmount, 6), 'USDC');

    // Execute the transfer transaction
    const hash = await walletClient.writeContract({
      address: USDC_CONTRACT_ADDRESS as `0x${string}`,
      abi: USDC_ABI,
      functionName: 'transfer',
      args: [PAYMENT_RECIPIENT as `0x${string}`, usdcAmount],
      account: userAccount as `0x${string}`,
    });

    console.log('Transaction hash:', hash);

    return {
      success: true,
      paymentResponse: {
        amount,
        description: description || 'LoRA image generation payment',
        timestamp: new Date().toISOString(),
        transactionId: hash,
        account: userAccount,
        recipient: PAYMENT_RECIPIENT,
        usdcAmount: formatUnits(usdcAmount, 6),
      },
    };
  } catch (error) {
    console.error('USDC payment error:', error);

    // Provide more specific error messages
    let errorMessage = 'Payment failed';
    if (error instanceof Error) {
      if (error.message.includes('User rejected')) {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message.includes('insufficient funds')) {
        errorMessage = 'Insufficient USDC balance';
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error - please check your connection';
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

// Mock x402 payment verification for development
export async function mockX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  // Simulate payment processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // For development, always return success
  return {
    success: true,
    paymentResponse: {
      amount,
      description,
      timestamp: new Date().toISOString(),
      transactionId: `mock_tx_${Date.now()}`,
    },
  };
}

import { wrapFetchWithPayment, decodeXPaymentResponse } from 'x402-fetch';
import { Account } from 'viem';

export interface X402PaymentResult {
  success: boolean;
  paymentResponse?: any;
  error?: string;
}

export interface X402PaymentRequest {
  url: string;
  amount: string; // e.g., "$1"
  description?: string;
}

// Create a mock x402 payment endpoint for testing
export const createX402PaymentEndpoint = (baseUrl: string) => {
  return `${baseUrl}/api/x402-payment`;
};

// Process x402 payment using the account
export async function processX402Payment(
  account: Account,
  paymentRequest: X402PaymentRequest
): Promise<X402PaymentResult> {
  try {
    // Wrap fetch with x402 payment capability
    const fetchWithPayment = wrapFetchWithPayment(fetch, account);
    
    // Make the payment request
    const response = await fetchWithPayment(paymentRequest.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: paymentRequest.amount,
        description: paymentRequest.description || 'LoRA image generation payment',
      }),
    });

    if (!response.ok) {
      throw new Error(`Payment failed: ${response.status} ${response.statusText}`);
    }

    const body = await response.json();
    
    // Decode payment response from headers
    const paymentResponseHeader = response.headers.get('x-payment-response');
    const paymentResponse = paymentResponseHeader 
      ? decodeXPaymentResponse(paymentResponseHeader)
      : null;

    return {
      success: true,
      paymentResponse,
    };
  } catch (error) {
    console.error('x402 payment error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment failed',
    };
  }
}

// Mock x402 payment verification for development
export async function mockX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  // Simulate payment processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // For development, always return success
  return {
    success: true,
    paymentResponse: {
      amount,
      description,
      timestamp: new Date().toISOString(),
      transactionId: `mock_tx_${Date.now()}`,
    },
  };
}

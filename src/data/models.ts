import { Model } from '@/types';

export const models: Model[] = [
  {
    id: 'zorby',
    provider: 'aaronj<PERSON><PERSON>',
    name: 'zorby',
    title: 'Zorby Character LoRA',
    description: 'A specialized LoRA for generating Zorby character variations with consistent style and features.',
    previewImage: '/zorby.jpg',
    updated: '3 days, 2 hours ago',
    runs: '125.7K',
    replicateModel: 'aaronjmars/zorbyv4',
    replicateVersion: '161322d667f86c95c0ee6338c9cd1ce5bd203e5422b5382b4debb2c610ae5ff0',
    basePrompt: 'ZRBY, blue spherical creature with glowing pink eyes and a happy smile'
  },
  {
    id: 'berserk',
    provider: 'aaronjmars',
    name: 'berserk',
    title: 'Berserk Manga Style LoRA',
    description: 'Generate images in the distinctive dark fantasy art style of Kentaro Miura\'s Berserk manga series.',
    previewImage: '/berserk.jpg',
    updated: '1 week, 3 days ago',
    runs: '89.2K',
    replicateModel: 'aaronjmars/b3rs3rk',
    replicateVersion: 'b5c4a137b84c053158fdbdb3800d1d1492b85026f9ce83fc2778f7a5db2355e3',
    basePrompt: 'TOK'
  },
  {
    id: 'ascii',
    provider: 'aaronjmars',
    name: 'ascii',
    title: 'ASCII Art Style LoRA',
    description: 'Transform images into ASCII art style with monospace character patterns and retro computer aesthetics.',
    previewImage: '/ascii.jpg',
    updated: '5 days, 1 hour ago',
    runs: '156.4K',
    replicateModel: 'aaronjmars/ascii',
    replicateVersion: '0bd02f3aa7d6a43fcf96184c0e540a1de273bdff7707572bd672107c639cd3f5',
    basePrompt: 'TOK'
  }
];

export const getModelById = (id: string): Model | undefined => {
  return models.find(model => model.id === id);
};
